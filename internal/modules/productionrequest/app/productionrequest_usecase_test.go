package app

import (
	"context"
	"testing"
	"time"

	clientModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
)

// Mock repositories for testing
type mockProductionRequestRepo struct {
	productionRequests map[string]*model.ProductionRequest
}

func (m *mockProductionRequestRepo) Create(ctx context.Context, pr model.ProductionRequest) error {
	m.productionRequests[pr.ID] = &pr
	return nil
}

func (m *mockProductionRequestRepo) Update(ctx context.Context, pr model.ProductionRequest) error {
	m.productionRequests[pr.ID] = &pr
	return nil
}

func (m *mockProductionRequestRepo) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionRequest, error) {
	if prop == "id" {
		if pr, exists := m.productionRequests[value]; exists {
			return pr, nil
		}
	}
	return nil, model.ProductionRequestNotFoundf("Production request not found", nil, nil)
}

func (m *mockProductionRequestRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return 0, nil
}

func (m *mockProductionRequestRepo) GetAll(ctx context.Context) ([]model.ProductionRequest, error) {
	var results []model.ProductionRequest
	for _, pr := range m.productionRequests {
		results = append(results, *pr)
	}
	return results, nil
}

func (m *mockProductionRequestRepo) Delete(ctx context.Context, id string) error {
	delete(m.productionRequests, id)
	return nil
}

func (m *mockProductionRequestRepo) GetProductionRequestItems(ctx context.Context, productionRequestID string) ([]model.ProductionRequestItem, error) {
	return []model.ProductionRequestItem{}, nil
}

func (m *mockProductionRequestRepo) CreateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemCreate) error {
	return nil
}

func (m *mockProductionRequestRepo) UpdateProductionRequestItems(ctx context.Context, productionRequestID string, items []model.ProductionRequestItemUpdate) error {
	return nil
}

func (m *mockProductionRequestRepo) DeleteProductionRequestItems(ctx context.Context, productionRequestID string) error {
	return nil
}

type mockClientRepo struct {
	clients map[string]*clientModel.Client
}

func (m *mockClientRepo) Create(ctx context.Context, client clientModel.Client) error {
	m.clients[client.ID] = &client
	return nil
}

func (m *mockClientRepo) Update(ctx context.Context, client clientModel.Client) error {
	m.clients[client.ID] = &client
	return nil
}

func (m *mockClientRepo) GetByProp(ctx context.Context, prop string, value string) (*clientModel.Client, error) {
	if prop == "id" {
		if client, exists := m.clients[value]; exists {
			return client, nil
		}
	}
	return nil, clientModel.ClientNotFoundf("Client not found", nil, nil)
}

func (m *mockClientRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return 0, nil
}

func (m *mockClientRepo) GetAll(ctx context.Context) ([]clientModel.Client, error) {
	var results []clientModel.Client
	for _, client := range m.clients {
		results = append(results, *client)
	}
	return results, nil
}

func (m *mockClientRepo) Delete(ctx context.Context, id string) error {
	delete(m.clients, id)
	return nil
}

type mockProductRepo struct{}

func (m *mockProductRepo) Create(ctx context.Context, product productModel.Product) error {
	return nil
}

func (m *mockProductRepo) Update(ctx context.Context, product productModel.Product) error {
	return nil
}

func (m *mockProductRepo) GetByProp(ctx context.Context, prop string, value string) (*productModel.Product, error) {
	return nil, nil
}

func (m *mockProductRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return 0, nil
}

func (m *mockProductRepo) GetAll(ctx context.Context) ([]productModel.Product, error) {
	return []productModel.Product{}, nil
}

func (m *mockProductRepo) Delete(ctx context.Context, id string) error {
	return nil
}

func (m *mockProductRepo) GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]productModel.Product, error) {
	return []productModel.Product{}, nil
}

func (m *mockProductRepo) CreateProductCategories(ctx context.Context, productID string, categoryIDs []string) error {
	return nil
}

func (m *mockProductRepo) UpdateProductCategories(ctx context.Context, productID string, categoryIDs []string) error {
	return nil
}

func (m *mockProductRepo) GetProductCategories(ctx context.Context, productID string) ([]string, error) {
	return []string{}, nil
}

func (m *mockProductRepo) DeleteProductCategories(ctx context.Context, productID string) error {
	return nil
}

func TestGetByPropWithClient(t *testing.T) {
	// Arrange
	ctx := context.Background()
	now := time.Now()
	fatherName := "John"
	motherName := "Jane"

	mockPRRepo := &mockProductionRequestRepo{
		productionRequests: make(map[string]*model.ProductionRequest),
	}
	mockClientRepo := &mockClientRepo{
		clients: make(map[string]*clientModel.Client),
	}
	mockProductRepo := &mockProductRepo{}

	// Create test data
	client := &clientModel.Client{
		ID:         "client-123",
		Name:       "Test Client",
		FatherName: &fatherName,
		MotherName: &motherName,
		ClientType: "natural",
	}
	mockClientRepo.clients["client-123"] = client

	productionRequest := &model.ProductionRequest{
		ID:           "pr-123",
		Code:         "PR-001",
		ClientID:     "client-123",
		ExpectedDate: &now,
		Priority:     "high",
		State:        "pending",
		Requests:     []model.ProductionRequestItem{},
		CreatedAt:    &now,
		UpdatedAt:    &now,
	}
	mockPRRepo.productionRequests["pr-123"] = productionRequest

	useCase := NewProductionRequestUsecase(mockPRRepo, mockClientRepo, mockProductRepo)

	// Act
	result, err := useCase.GetByPropWithClient(ctx, "id", "pr-123")

	// Assert
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if result == nil {
		t.Fatal("Expected result to not be nil")
	}
	if result.ProductionRequest.ID != "pr-123" {
		t.Errorf("Expected ProductionRequest.ID to be 'pr-123', got %s", result.ProductionRequest.ID)
	}
	if result.Client == nil {
		t.Fatal("Expected Client to not be nil")
	}

	clientResult := result.Client.(*clientModel.Client)
	if clientResult.ID != "client-123" {
		t.Errorf("Expected Client.ID to be 'client-123', got %s", clientResult.ID)
	}
	if clientResult.Name != "Test Client" {
		t.Errorf("Expected Client.Name to be 'Test Client', got %s", clientResult.Name)
	}
	if clientResult.ClientType != "natural" {
		t.Errorf("Expected Client.ClientType to be 'natural', got %s", clientResult.ClientType)
	}
}

func TestGetAllWithClient(t *testing.T) {
	// Arrange
	ctx := context.Background()
	now := time.Now()
	fatherName := "John"
	motherName := "Jane"

	mockPRRepo := &mockProductionRequestRepo{
		productionRequests: make(map[string]*model.ProductionRequest),
	}
	mockClientRepo := &mockClientRepo{
		clients: make(map[string]*clientModel.Client),
	}
	mockProductRepo := &mockProductRepo{}

	// Create test data
	client := &clientModel.Client{
		ID:         "client-123",
		Name:       "Test Client",
		FatherName: &fatherName,
		MotherName: &motherName,
		ClientType: "natural",
	}
	mockClientRepo.clients["client-123"] = client

	productionRequest := &model.ProductionRequest{
		ID:           "pr-123",
		Code:         "PR-001",
		ClientID:     "client-123",
		ExpectedDate: &now,
		Priority:     "high",
		State:        "pending",
		Requests:     []model.ProductionRequestItem{},
		CreatedAt:    &now,
		UpdatedAt:    &now,
	}
	mockPRRepo.productionRequests["pr-123"] = productionRequest

	useCase := NewProductionRequestUsecase(mockPRRepo, mockClientRepo, mockProductRepo)

	// Act
	results, err := useCase.GetAllWithClient(ctx)

	// Assert
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if len(results) != 1 {
		t.Fatalf("Expected 1 result, got %d", len(results))
	}

	result := results[0]
	if result.ProductionRequest.ID != "pr-123" {
		t.Errorf("Expected ProductionRequest.ID to be 'pr-123', got %s", result.ProductionRequest.ID)
	}
	if result.Client == nil {
		t.Fatal("Expected Client to not be nil")
	}

	clientResult := result.Client.(*clientModel.Client)
	if clientResult.ID != "client-123" {
		t.Errorf("Expected Client.ID to be 'client-123', got %s", clientResult.ID)
	}
	if clientResult.Name != "Test Client" {
		t.Errorf("Expected Client.Name to be 'Test Client', got %s", clientResult.Name)
	}
}
