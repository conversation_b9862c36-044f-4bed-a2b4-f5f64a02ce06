package app

import (
	"context"

	clientModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
)

type productionRequestUsecase struct {
	repo        model.ProductionRequestRepository
	clientRepo  clientModel.ClientRepository
	productRepo productModel.ProductRepository
}

func NewProductionRequestUsecase(
	repo model.ProductionRequestRepository,
	clientRepo clientModel.ClientRepository,
	productRepo productModel.ProductRepository,
) model.ProductionRequestUsecase {
	return &productionRequestUsecase{
		repo:        repo,
		clientRepo:  clientRepo,
		productRepo: productRepo,
	}
}

// Delete implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) Delete(ctx context.Context, id string) error {
	return p.repo.Delete(ctx, id)
}

// GetAll implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) GetAll(ctx context.Context) ([]model.ProductionRequest, error) {
	return p.repo.GetAll(ctx)
}

// GetByProp implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionRequest, error) {
	return p.repo.GetByProp(ctx, prop, value)
}

// GetByPropWithClient implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) GetByPropWithClient(ctx context.Context, prop string, value string) (*model.ProductionRequestWithDetails, error) {
	// Get the production request
	productionRequest, err := p.repo.GetByProp(ctx, prop, value)
	if err != nil {
		return nil, err
	}

	// Get the client details
	client, err := p.clientRepo.GetByProp(ctx, "id", productionRequest.ClientID)
	if err != nil {
		return nil, err
	}

	// Create the result with client details
	result := &model.ProductionRequestWithDetails{
		ProductionRequest: *productionRequest,
		Client:            client,
		RequestItems:      make([]model.ProductionRequestItemWithProduct, len(productionRequest.Requests)),
	}

	// For now, just copy the request items without product details
	for i, item := range productionRequest.Requests {
		result.RequestItems[i] = model.ProductionRequestItemWithProduct{
			ProductionRequestItem: item,
			Product:               nil, // Could be populated later if needed
		}
	}

	return result, nil
}

// GetAllWithClient implements model.ProductionRequestUsecase.
func (p *productionRequestUsecase) GetAllWithClient(ctx context.Context) ([]model.ProductionRequestWithDetails, error) {
	// Get all production requests
	productionRequests, err := p.repo.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	var results []model.ProductionRequestWithDetails
	for _, pr := range productionRequests {
		// Get the client details for each production request
		client, err := p.clientRepo.GetByProp(ctx, "id", pr.ClientID)
		if err != nil {
			return nil, err
		}

		// Create the result with client details
		result := model.ProductionRequestWithDetails{
			ProductionRequest: pr,
			Client:            client,
			RequestItems:      make([]model.ProductionRequestItemWithProduct, len(pr.Requests)),
		}

		// For now, just copy the request items without product details
		for i, item := range pr.Requests {
			result.RequestItems[i] = model.ProductionRequestItemWithProduct{
				ProductionRequestItem: item,
				Product:               nil, // Could be populated later if needed
			}
		}

		results = append(results, result)
	}

	return results, nil
}
