package rest

import (
	"net/http"
	"time"

	clientModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type clientResultDTO struct {
	ID         string  `json:"id"`
	Name       string  `json:"name"`
	FatherName *string `json:"father_name"`
	MotherName *string `json:"mother_name"`
	ClientType string  `json:"client_type"`
}

type productionRequestResult struct {
	ID           string                           `json:"id"`
	Code         string                           `json:"code"`
	Client       clientResultDTO                  `json:"client"`
	ExpectedDate *time.Time                       `json:"expected_date"`
	Priority     string                           `json:"priority"`
	State        string                           `json:"state"`
	Requests     []productionRequestItemResultDTO `json:"requests"`
	CreatedAt    *time.Time                       `json:"created_at"`
	UpdatedAt    *time.Time                       `json:"updated_at"`
}

type productionRequestItemResultDTO struct {
	ID                  string  `json:"id"`
	ProductionRequestID string  `json:"production_request_id"`
	ProductID           string  `json:"product_id"`
	Quantity            float64 `json:"quantity"`
}

func clientToResultDTO(client *clientModel.Client) clientResultDTO {
	return clientResultDTO{
		ID:         client.ID,
		Name:       client.Name,
		FatherName: client.FatherName,
		MotherName: client.MotherName,
		ClientType: client.ClientType,
	}
}

func productionRequestWithDetailsToResult(prwd model.ProductionRequestWithDetails) productionRequestResult {
	requests := make([]productionRequestItemResultDTO, len(prwd.ProductionRequest.Requests))
	for i, item := range prwd.ProductionRequest.Requests {
		requests[i] = productionRequestItemResultDTO{
			ID:                  item.ID,
			ProductionRequestID: item.ProductionRequestID,
			ProductID:           item.ProductID,
			Quantity:            item.Quantity,
		}
	}

	// Convert client interface to client model
	client := prwd.Client.(*clientModel.Client)

	return productionRequestResult{
		ID:           prwd.ProductionRequest.ID,
		Code:         prwd.ProductionRequest.Code,
		Client:       clientToResultDTO(client),
		ExpectedDate: prwd.ProductionRequest.ExpectedDate,
		Priority:     prwd.ProductionRequest.Priority,
		State:        prwd.ProductionRequest.State,
		Requests:     requests,
		CreatedAt:    prwd.ProductionRequest.CreatedAt,
		UpdatedAt:    prwd.ProductionRequest.UpdatedAt,
	}
}

// GetById implements ProductionRequestHandler.
func (p *productionRequestHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	productionRequestWithDetails, err := p.useCase.GetByPropWithClient(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production request")
		return
	}

	rest.SuccessDResponse(w, r, productionRequestWithDetailsToResult(*productionRequestWithDetails), http.StatusOK)
}

// GetAll implements ProductionRequestHandler.
func (p *productionRequestHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	productionRequestsWithDetails, err := p.useCase.GetAllWithClient(ctx)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production requests")
		return
	}

	var results []productionRequestResult
	for _, prwd := range productionRequestsWithDetails {
		results = append(results, productionRequestWithDetailsToResult(prwd))
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
