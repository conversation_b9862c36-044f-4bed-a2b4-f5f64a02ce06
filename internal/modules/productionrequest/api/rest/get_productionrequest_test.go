package rest

import (
	"testing"
	"time"

	clientModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionrequest/model"
)

func TestClientToResultDTO(t *testing.T) {
	// Arrange
	fatherName := "John"
	motherName := "Jane"
	client := &clientModel.Client{
		ID:         "client-123",
		Name:       "Test Client",
		FatherName: &fatherName,
		MotherName: &motherName,
		ClientType: "natural",
	}

	// Act
	result := clientToResultDTO(client)

	// Assert
	if result.ID != "client-123" {
		t.<PERSON><PERSON><PERSON>("Expected ID to be 'client-123', got %s", result.ID)
	}
	if result.Name != "Test Client" {
		t.Errorf("Expected Name to be 'Test Client', got %s", result.Name)
	}
	if result.FatherName == nil || *result.FatherName != "<PERSON>" {
		t.<PERSON>("Expected <PERSON>N<PERSON> to be '<PERSON>', got %v", result.FatherName)
	}
	if result.MotherName == nil || *result.MotherName != "Jane" {
		t.<PERSON><PERSON><PERSON>("Expected MotherName to be '<PERSON>', got %v", result.MotherName)
	}
	if result.ClientType != "natural" {
		t.Errorf("Expected ClientType to be 'natural', got %s", result.ClientType)
	}
}

func TestProductionRequestWithDetailsToResult(t *testing.T) {
	// Arrange
	now := time.Now()
	fatherName := "John"
	motherName := "Jane"
	
	client := &clientModel.Client{
		ID:         "client-123",
		Name:       "Test Client",
		FatherName: &fatherName,
		MotherName: &motherName,
		ClientType: "natural",
	}

	productionRequest := model.ProductionRequest{
		ID:           "pr-123",
		Code:         "PR-001",
		ClientID:     "client-123",
		ExpectedDate: &now,
		Priority:     "high",
		State:        "pending",
		Requests: []model.ProductionRequestItem{
			{
				ID:                  "item-1",
				ProductionRequestID: "pr-123",
				ProductID:           "product-1",
				Quantity:            10.5,
			},
		},
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	prwd := model.ProductionRequestWithDetails{
		ProductionRequest: productionRequest,
		Client:            client,
		RequestItems:      []model.ProductionRequestItemWithProduct{},
	}

	// Act
	result := productionRequestWithDetailsToResult(prwd)

	// Assert
	if result.ID != "pr-123" {
		t.Errorf("Expected ID to be 'pr-123', got %s", result.ID)
	}
	if result.Code != "PR-001" {
		t.Errorf("Expected Code to be 'PR-001', got %s", result.Code)
	}
	if result.Client.ID != "client-123" {
		t.Errorf("Expected Client.ID to be 'client-123', got %s", result.Client.ID)
	}
	if result.Client.Name != "Test Client" {
		t.Errorf("Expected Client.Name to be 'Test Client', got %s", result.Client.Name)
	}
	if result.Client.ClientType != "natural" {
		t.Errorf("Expected Client.ClientType to be 'natural', got %s", result.Client.ClientType)
	}
	if result.Priority != "high" {
		t.Errorf("Expected Priority to be 'high', got %s", result.Priority)
	}
	if result.State != "pending" {
		t.Errorf("Expected State to be 'pending', got %s", result.State)
	}
	if len(result.Requests) != 1 {
		t.Errorf("Expected 1 request item, got %d", len(result.Requests))
	}
	if len(result.Requests) > 0 {
		if result.Requests[0].ID != "item-1" {
			t.Errorf("Expected request item ID to be 'item-1', got %s", result.Requests[0].ID)
		}
		if result.Requests[0].ProductID != "product-1" {
			t.Errorf("Expected request item ProductID to be 'product-1', got %s", result.Requests[0].ProductID)
		}
		if result.Requests[0].Quantity != 10.5 {
			t.Errorf("Expected request item Quantity to be 10.5, got %f", result.Requests[0].Quantity)
		}
	}
}
