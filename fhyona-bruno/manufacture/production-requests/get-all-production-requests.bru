meta {
  name: Get All Production Requests
  type: http
  seq: 1
}

get {
  url: {{baseUrl}}/api/v1/production-requests
  body: none
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return array of production requests", function() {
    expect(res.getBody()).to.be.an('array');
  });
  
  test("each production request should have client object instead of client_id", function() {
    const body = res.getBody();
    if (body.length > 0) {
      expect(body[0]).to.have.property('client');
      expect(body[0].client).to.be.an('object');
      expect(body[0].client).to.have.property('id');
      expect(body[0].client).to.have.property('name');
      expect(body[0].client).to.have.property('client_type');
      expect(body[0]).to.not.have.property('client_id');
    }
  });
}
