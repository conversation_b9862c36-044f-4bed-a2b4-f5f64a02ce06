meta {
  name: Get Production Request By ID
  type: http
  seq: 2
}

get {
  url: {{baseUrl}}/api/v1/production-requests/{{productionRequestId}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return production request with client object", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
    expect(body).to.have.property('client');
    expect(body.client).to.be.an('object');
    expect(body.client).to.have.property('id');
    expect(body.client).to.have.property('name');
    expect(body.client).to.have.property('client_type');
    expect(body).to.not.have.property('client_id');
  });
  
  test("client should have basic information", function() {
    const body = res.getBody();
    expect(body.client).to.have.property('father_name');
    expect(body.client).to.have.property('mother_name');
  });
}
